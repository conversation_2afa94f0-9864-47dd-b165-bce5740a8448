'use client';

import React from 'react';
import { CreditCard, Banknote, Percent } from 'lucide-react';
import { PaymentConfiguration, PartialPaymentCalculation } from '@/types/api';

interface PaymentMethodSelectorProps {
  config: PaymentConfiguration;
  partialPayment: PartialPaymentCalculation | null;
  selectedMethod: 'razorpay' | 'cod';
  selectedAmount: 'full' | 'partial';
  onMethodChange: (method: 'razorpay' | 'cod') => void;
  onAmountChange: (amount: 'full' | 'partial') => void;
  totalAmount: string;
}

export const PaymentMethodSelector: React.FC<PaymentMethodSelectorProps> = ({
  config,
  partialPayment,
  selectedMethod,
  selectedAmount,
  onMethodChange,
  onAmountChange,
  totalAmount,
}) => {
  const codCharge = parseFloat(config.cod_charge_percentage);
  const codAmount = parseFloat(totalAmount) * (codCharge / 100);
  const totalWithCOD = parseFloat(totalAmount) + codAmount;

  return (
    <div className="space-y-6">
      {/* Payment Amount Selection */}
      {partialPayment?.requires_partial_payment && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900">Payment Amount</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Partial Payment Option */}
            <div
              className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                selectedAmount === 'partial'
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => onAmountChange('partial')}
            >
              <div className="flex items-center space-x-3">
                <div className={`p-2 rounded-full ${
                  selectedAmount === 'partial' ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-600'
                }`}>
                  <Percent className="h-5 w-5" />
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">Pay Advance</h4>
                  <p className="text-sm text-gray-600">Pay now, rest on service completion</p>
                  <p className="text-lg font-bold text-blue-600">₹{partialPayment.partial_payment_amount}</p>
                  <p className="text-xs text-gray-500">
                    Remaining: ₹{partialPayment.remaining_amount}
                  </p>
                </div>
              </div>
            </div>

            {/* Full Payment Option */}
            <div
              className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                selectedAmount === 'full'
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => onAmountChange('full')}
            >
              <div className="flex items-center space-x-3">
                <div className={`p-2 rounded-full ${
                  selectedAmount === 'full' ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-600'
                }`}>
                  <CreditCard className="h-5 w-5" />
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">Pay Full Amount</h4>
                  <p className="text-sm text-gray-600">Pay complete amount now</p>
                  <p className="text-lg font-bold text-green-600">₹{partialPayment.total_amount}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Services with Partial Payment Info */}
          {partialPayment.services_with_partial_payment.length > 0 && (
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">Services requiring advance payment:</h4>
              <ul className="space-y-1">
                {partialPayment.services_with_partial_payment.map((service, index) => (
                  <li key={index} className="text-sm text-blue-800">
                    • {service.service_title}: ₹{service.partial_payment_amount} 
                    ({service.partial_payment_type === 'percentage' 
                      ? `${service.partial_payment_value}%` 
                      : `₹${service.partial_payment_value}`})
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      {/* Payment Method Selection */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Payment Method</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Razorpay Option */}
          {config.enable_razorpay && (
            <div
              className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                selectedMethod === 'razorpay'
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => onMethodChange('razorpay')}
            >
              <div className="flex items-center space-x-3">
                <div className={`p-2 rounded-full ${
                  selectedMethod === 'razorpay' ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-600'
                }`}>
                  <CreditCard className="h-5 w-5" />
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">Online Payment</h4>
                  <p className="text-sm text-gray-600">Pay securely with card, UPI, or wallet</p>
                  <p className="text-xs text-green-600 mt-1">✓ Instant confirmation</p>
                </div>
              </div>
            </div>
          )}

          {/* COD Option */}
          {config.enable_cod && parseFloat(totalAmount) >= parseFloat(config.cod_minimum_order) && (
            <div
              className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                selectedMethod === 'cod'
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => onMethodChange('cod')}
            >
              <div className="flex items-center space-x-3">
                <div className={`p-2 rounded-full ${
                  selectedMethod === 'cod' ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-600'
                }`}>
                  <Banknote className="h-5 w-5" />
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">Cash on Delivery</h4>
                  <p className="text-sm text-gray-600">Pay when service is completed</p>
                  {codCharge > 0 && (
                    <p className="text-xs text-orange-600 mt-1">
                      + ₹{codAmount.toFixed(2)} COD charge ({config.cod_charge_percentage}%)
                    </p>
                  )}
                  <p className="text-sm font-medium text-gray-900 mt-1">
                    Total: ₹{totalWithCOD.toFixed(2)}
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* COD Minimum Order Warning */}
        {config.enable_cod && parseFloat(totalAmount) < parseFloat(config.cod_minimum_order) && (
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
            <p className="text-sm text-orange-800">
              Cash on Delivery is available for orders above ₹{config.cod_minimum_order}. 
              Your order total is ₹{totalAmount}.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};
