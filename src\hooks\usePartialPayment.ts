import { useState, useCallback } from 'react';
import { paymentApi } from '@/lib/api';
import { PartialPaymentCalculation, PartialPaymentItem } from '@/types/api';

interface UsePartialPaymentReturn {
  calculation: PartialPaymentCalculation | null;
  isLoading: boolean;
  error: string | null;
  calculatePartialPayment: (items: PartialPaymentItem[]) => Promise<void>;
  clearCalculation: () => void;
}

export const usePartialPayment = (): UsePartialPaymentReturn => {
  const [calculation, setCalculation] = useState<PartialPaymentCalculation | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const calculatePartialPayment = useCallback(async (items: PartialPaymentItem[]) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await paymentApi.calculatePartialPayment({ items });
      
      if (response.success) {
        setCalculation(response);
      } else {
        setError('Failed to calculate partial payment');
      }
    } catch (err: any) {
      console.error('Partial payment calculation error:', err);
      setError(err.message || 'Failed to calculate partial payment');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const clearCalculation = useCallback(() => {
    setCalculation(null);
    setError(null);
  }, []);

  return {
    calculation,
    isLoading,
    error,
    calculatePartialPayment,
    clearCalculation,
  };
};
