'use client';

import React, { useState, useEffect } from 'react';
import { MainLayout } from '@/components/layout/MainLayout';
import { useAuth } from '@/contexts/AuthContext';
import { usePaymentConfig } from '@/hooks/usePaymentConfig';
import { usePartialPayment } from '@/hooks/usePartialPayment';
import { paymentApi } from '@/lib/api';

export default function TestPaymentPage() {
  const { isAuthenticated, user } = useAuth();
  const { config, isLoading: configLoading, error: configError } = usePaymentConfig();
  const { calculation, calculatePartialPayment, isLoading: partialLoading } = usePartialPayment();
  const [testResults, setTestResults] = useState<any>(null);

  const testPaymentAPIs = async () => {
    if (!isAuthenticated) {
      alert('Please login first to test payment APIs');
      return;
    }

    try {
      // Test partial payment calculation
      const testItems = [
        { service_id: 1, quantity: 1 },
        { service_id: 2, quantity: 2 }
      ];

      await calculatePartialPayment(testItems);

      setTestResults({
        timestamp: new Date().toISOString(),
        config_loaded: !!config,
        partial_calculation_done: true,
      });
    } catch (error: any) {
      console.error('Payment API test failed:', error);
      setTestResults({ error: error.message });
    }
  };

  return (
    <MainLayout>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Payment Integration Test</h1>
          <p className="text-gray-600">Testing the new Razorpay and partial payment features</p>
        </div>

        {/* Authentication Status */}
        <div className="mb-8 p-4 rounded-lg border">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">
                Authentication Status: 
                <span className={`ml-2 ${isAuthenticated ? 'text-green-600' : 'text-red-600'}`}>
                  {isAuthenticated ? '✅ Authenticated' : '❌ Not Authenticated'}
                </span>
              </p>
              {user && (
                <p className="text-sm text-gray-600 mt-1">
                  Logged in as: {user.name} ({user.mobile_number})
                </p>
              )}
            </div>
            {!isAuthenticated && (
              <button
                onClick={() => window.location.href = '/auth/login'}
                className="btn-primary"
              >
                Login to Test
              </button>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Payment Configuration */}
          <div className="card p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Payment Configuration</h2>
            
            {configLoading ? (
              <p className="text-gray-600">Loading configuration...</p>
            ) : configError ? (
              <p className="text-red-600">Error: {configError}</p>
            ) : config ? (
              <div className="space-y-3">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="font-medium text-gray-700">Razorpay Enabled:</p>
                    <p className={config.enable_razorpay ? 'text-green-600' : 'text-red-600'}>
                      {config.enable_razorpay ? '✅ Yes' : '❌ No'}
                    </p>
                  </div>
                  <div>
                    <p className="font-medium text-gray-700">COD Enabled:</p>
                    <p className={config.enable_cod ? 'text-green-600' : 'text-red-600'}>
                      {config.enable_cod ? '✅ Yes' : '❌ No'}
                    </p>
                  </div>
                  <div>
                    <p className="font-medium text-gray-700">Environment:</p>
                    <p className="text-blue-600">{config.environment}</p>
                  </div>
                  <div>
                    <p className="font-medium text-gray-700">COD Charge:</p>
                    <p className="text-orange-600">{config.cod_charge_percentage}%</p>
                  </div>
                  <div>
                    <p className="font-medium text-gray-700">COD Minimum:</p>
                    <p className="text-gray-600">₹{config.cod_minimum_order}</p>
                  </div>
                  <div>
                    <p className="font-medium text-gray-700">Razorpay Key:</p>
                    <p className="text-xs text-gray-600 font-mono">
                      {config.razorpay_key_id.substring(0, 12)}...
                    </p>
                  </div>
                </div>
              </div>
            ) : (
              <p className="text-gray-600">No configuration loaded</p>
            )}
          </div>

          {/* Partial Payment Test */}
          <div className="card p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Partial Payment Test</h2>
            
            <div className="space-y-4">
              <button
                onClick={testPaymentAPIs}
                disabled={!isAuthenticated || partialLoading}
                className="btn-primary w-full"
              >
                {partialLoading ? 'Testing...' : 'Test Partial Payment API'}
              </button>

              {calculation && (
                <div className="space-y-3">
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <h3 className="font-medium text-blue-900 mb-2">Calculation Results:</h3>
                    <div className="space-y-1 text-sm">
                      <p><strong>Requires Partial Payment:</strong> 
                        <span className={calculation.requires_partial_payment ? 'text-green-600' : 'text-red-600'}>
                          {calculation.requires_partial_payment ? ' Yes' : ' No'}
                        </span>
                      </p>
                      <p><strong>Total Amount:</strong> ₹{calculation.total_amount}</p>
                      {calculation.requires_partial_payment && (
                        <>
                          <p><strong>Partial Payment:</strong> ₹{calculation.partial_payment_amount}</p>
                          <p><strong>Remaining Amount:</strong> ₹{calculation.remaining_amount}</p>
                        </>
                      )}
                    </div>
                  </div>

                  {calculation.services_with_partial_payment.length > 0 && (
                    <div className="bg-green-50 p-4 rounded-lg">
                      <h4 className="font-medium text-green-900 mb-2">Services with Partial Payment:</h4>
                      <ul className="space-y-1 text-sm">
                        {calculation.services_with_partial_payment.map((service, index) => (
                          <li key={index} className="text-green-800">
                            • {service.service_title}: ₹{service.partial_payment_amount} 
                            ({service.partial_payment_type === 'percentage' 
                              ? `${service.partial_payment_value}%` 
                              : `₹${service.partial_payment_value}`})
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* API Test Results */}
        {testResults && (
          <div className="mt-8 card p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Test Results</h2>
            <div className="bg-gray-50 p-4 rounded-lg">
              <pre className="text-sm">{JSON.stringify(testResults, null, 2)}</pre>
            </div>
          </div>
        )}

        {/* Navigation */}
        <div className="mt-8 flex space-x-4">
          <button
            onClick={() => window.location.href = '/cart'}
            className="btn-outline"
          >
            Go to Cart
          </button>
          
          <button
            onClick={() => window.location.href = '/checkout/payment'}
            className="btn-outline"
          >
            Go to Checkout
          </button>
        </div>
      </div>
    </MainLayout>
  );
}
