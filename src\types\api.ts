// Authentication Types
export interface User {
  id: number;
  email?: string;
  mobile_number?: string;
  name: string;
  user_type: 'CUSTOMER' | 'PROVIDER' | 'STAFF';
  is_verified: boolean;
  profile_picture?: string;
  date_joined: string;
}

export interface AuthTokens {
  access: string;
  refresh: string;
}

export interface LoginResponse {
  message: string;
  user: User;
  tokens: AuthTokens;
}

export interface RegisterResponse {
  message: string;
  user: User;
}

export interface OTPResponse {
  success: boolean;
  message: string;
  mobile_number: string;
}

// Address Types
export interface Address {
  id: number;
  address_type: 'HOME' | 'WORK' | 'OTHER';
  street: string;
  city: string;
  state: string;
  zip_code: string;
  landmark?: string;
  is_default: boolean;
  created_at: string;
}

// Paginated Response Types
export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

// Category Types
export interface Category {
  id: number;
  name: string;
  slug: string;
  image?: string;
  description: string;
  parent?: number;
  parent_name?: string;
  level: number;
  services_count: number;
  is_active: boolean;
  children?: Category[];
  services?: Service[];
  created_at?: string;
}

// Service Types
export interface Service {
  id: number;
  title: string;
  slug: string;
  image?: string;
  description: string;
  base_price: string;
  discount_price?: string;
  current_price: string;
  discount_percentage?: number;
  time_to_complete: string;
  category: number;
  category_name: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  // 🆕 Partial Payment Fields
  requires_partial_payment?: boolean;
  partial_payment_type?: 'percentage' | 'fixed';
  partial_payment_value?: string;
  partial_payment_description?: string;
}

// GST Breakdown Types
export interface TaxBreakdownItem {
  type: string;
  rate: string;
  amount: string;
}

// Cart Types
export interface CartItem {
  id: number;
  service: number;
  service_title: string;
  service_slug: string;
  service_image?: string;
  service_category: string;
  quantity: number;
  price_at_add: string;
  discount_at_add: string;
  current_service_price: string;
  total_price: string;
  savings: string;
  added_at: string;
}

export interface Cart {
  id: number | null;
  user?: number;
  session_key?: string;
  created_at: string | null;
  updated_at: string | null;
  is_active: boolean;
  sub_total: string;
  tax_amount: string;
  discount_amount: string;
  minimum_order_fee_applied: string;
  total_amount: string;
  coupon_code_applied?: string;
  // 🆕 NEW GST BREAKDOWN FIELDS
  cgst_amount: string;
  sgst_amount: string;
  igst_amount: string;
  ugst_amount: string;
  service_charge: string;
  tax_breakdown: TaxBreakdownItem[];
  items: CartItem[];
  items_count: number;
  unique_services_count: number;
}

export interface CartSummary {
  id: number;
  sub_total: string;
  tax_amount: string;
  discount_amount: string;
  minimum_order_fee_applied: string;
  total_amount: string;
  coupon_code_applied?: string;
  // 🆕 NEW GST BREAKDOWN FIELDS
  cgst_amount: string;
  sgst_amount: string;
  igst_amount: string;
  ugst_amount: string;
  service_charge: string;
  tax_breakdown: TaxBreakdownItem[];
  items_count: number;
  unique_services_count: number;
  updated_at: string;
}

// Order Types
export interface Order {
  id: number;
  order_number: string;
  customer: number;
  status: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled';
  payment_status: 'pending' | 'paid' | 'failed' | 'refunded';
  payment_method: 'razorpay' | 'cash_on_delivery';
  sub_total: string;
  tax_amount: string;
  discount_amount: string;
  total_amount: string;
  // 🆕 NEW GST BREAKDOWN FIELDS
  cgst_amount: string;
  sgst_amount: string;
  igst_amount: string;
  ugst_amount: string;
  service_charge: string;
  tax_breakdown: TaxBreakdownItem[];
  scheduled_date: string;
  scheduled_time: string;
  assigned_provider?: number;
  delivery_address: Address;
  special_instructions?: string;
  cancellation_reason?: string;
  created_at: string;
  updated_at: string;
}

// Payment Configuration Types
export interface PaymentConfiguration {
  enable_razorpay: boolean;
  enable_cod: boolean;
  cod_charge_percentage: string;
  cod_minimum_order: string;
  environment: 'test' | 'live';
  razorpay_key_id: string;
}

export interface PaymentConfigurationResponse {
  success: boolean;
  configuration: PaymentConfiguration;
}

// Partial Payment Types
export interface PartialPaymentItem {
  service_id: number;
  quantity: number;
}

export interface PartialPaymentCalculation {
  success: boolean;
  requires_partial_payment: boolean;
  total_amount: string;
  partial_payment_amount: string;
  remaining_amount: string;
  services_with_partial_payment: Array<{
    service_id: number;
    service_title: string;
    partial_payment_type: 'percentage' | 'fixed';
    partial_payment_value: string;
    partial_payment_amount: string;
    description: string;
  }>;
}

// Payment Types
export interface PaymentInitiateResponse {
  success: boolean;
  transaction_id: string;
  order_number: string;
  amount: string;
  currency: string;
  razorpay_key_id: string;
  razorpay_order_id: string;
  payment_gateway_data: {
    razorpay_order_id: string;
    amount: number;
    currency: string;
    key: string;
  };
}

export interface PaymentTransaction {
  transaction_id: string;
  order_number: string;
  amount: string;
  status: 'pending' | 'success' | 'failed' | 'refunded';
  payment_method: string;
  created_at: string;
  updated_at: string;
}

export interface RazorpayCallbackData {
  transaction_id: string;
  razorpay_payment_id: string;
  razorpay_order_id: string;
  razorpay_signature: string;
}

// Coupon Types
export interface Coupon {
  code: string;
  name: string;
  discount_type: 'percentage' | 'fixed';
  discount_value: string;
  minimum_order_amount?: string;
  maximum_discount_amount?: string;
  usage_limit?: number;
  usage_limit_per_user?: number;
  valid_from: string;
  valid_until: string;
  is_active: boolean;
}

export interface CouponValidationResponse {
  is_valid: boolean;
  message: string;
  discount_amount?: string;
  applied_coupon?: {
    code: string;
    name: string;
  };
}

// API Response Types
export interface ApiResponse<T> {
  data?: T;
  message?: string;
  error?: string;
}

export interface ApiError {
  message: string;
  details?: Record<string, string[]>;
  status?: number;
}
