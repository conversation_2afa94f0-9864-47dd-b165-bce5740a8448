import { useState, useEffect } from 'react';
import { paymentApi } from '@/lib/api';
import { PaymentConfiguration } from '@/types/api';

interface UsePaymentConfigReturn {
  config: PaymentConfiguration | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const usePaymentConfig = (): UsePaymentConfigReturn => {
  const [config, setConfig] = useState<PaymentConfiguration | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchConfig = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await paymentApi.getConfiguration();
      
      if (response.success) {
        setConfig(response.configuration);
      } else {
        setError('Failed to fetch payment configuration');
      }
    } catch (err: any) {
      console.error('Payment config fetch error:', err);
      setError(err.message || 'Failed to fetch payment configuration');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchConfig();
  }, []);

  return {
    config,
    isLoading,
    error,
    refetch: fetchConfig,
  };
};
