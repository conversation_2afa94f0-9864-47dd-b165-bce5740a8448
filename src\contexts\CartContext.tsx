'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { Cart, CartSummary, Service } from '@/types/api';
import { cartApi } from '@/lib/api';
import { useAuth } from './AuthContext';

interface CartContextType {
  cart: Cart | null;
  cartSummary: CartSummary | null;
  isLoading: boolean;
  addToCart: (service: Service, quantity?: number) => Promise<void>;
  updateCartItem: (itemId: number, quantity: number) => Promise<void>;
  removeCartItem: (itemId: number) => Promise<void>;
  clearCart: () => Promise<void>;
  applyCoupon: (couponCode: string) => Promise<void>;
  removeCoupon: () => Promise<void>;
  refreshCart: () => Promise<void>;
  getTotalItems: () => number;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export const useCart = () => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};

interface CartProviderProps {
  children: ReactNode;
}

export const CartProvider: React.FC<CartProviderProps> = ({ children }) => {
  const [cart, setCart] = useState<Cart | null>(null);
  const [cartSummary, setCartSummary] = useState<CartSummary | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { isAuthenticated } = useAuth();

  // Fetch cart data
  const refreshCart = async () => {
    setIsLoading(true);
    try {
      console.log('Refreshing cart data, authenticated:', isAuthenticated);

      // Fetch cart data (which includes GST breakdown)
      const cartData = await cartApi.getCart();
      console.log('Cart data received:', cartData);

      setCart(cartData as Cart);

      // Use cart data for summary as well since it contains all the summary fields plus GST breakdown
      if (cartData) {
        const summaryFromCart: CartSummary = {
          id: cartData.id || 0,
          sub_total: cartData.sub_total,
          tax_amount: cartData.tax_amount,
          discount_amount: cartData.discount_amount,
          minimum_order_fee_applied: cartData.minimum_order_fee_applied,
          total_amount: cartData.total_amount,
          coupon_code_applied: cartData.coupon_code_applied,
          cgst_amount: cartData.cgst_amount,
          sgst_amount: cartData.sgst_amount,
          igst_amount: cartData.igst_amount,
          ugst_amount: cartData.ugst_amount,
          service_charge: cartData.service_charge,
          tax_breakdown: cartData.tax_breakdown,
          items_count: cartData.items_count,
          unique_services_count: cartData.unique_services_count,
          updated_at: cartData.updated_at || new Date().toISOString(),
        };
        setCartSummary(summaryFromCart);
      } else {
        setCartSummary(null);
      }
    } catch (error: any) {
      console.error('Failed to fetch cart:', error);

      // Handle different error scenarios
      if (error.status === 401) {
        console.log('Cart fetch failed due to authentication - user might need to login');
      } else if (error.status === 404) {
        console.log('Cart not found - initializing empty cart');
      }

      // Initialize empty cart state
      setCart(null);
      setCartSummary(null);
    } finally {
      setIsLoading(false);
    }
  };

  // Initialize cart on mount and when auth state changes
  useEffect(() => {
    // Only fetch cart data if user is authenticated
    if (isAuthenticated) {
      refreshCart();
    } else {
      // Clear cart data when user is not authenticated
      setCart(null);
      setCartSummary(null);
      setIsLoading(false);
    }
  }, [isAuthenticated]);

  const addToCart = async (service: Service, quantity: number = 1) => {
    try {
      console.log('Adding to cart:', service.id, 'quantity:', quantity, 'authenticated:', isAuthenticated);

      const response = await cartApi.addToCart(service.id, quantity);
      console.log('Add to cart response:', response);

      await refreshCart();
    } catch (error: any) {
      console.error('Failed to add to cart:', error);

      // Provide more specific error information
      if (error.status === 401) {
        throw new Error('Please login to add items to cart');
      } else if (error.status === 404) {
        throw new Error('Service not found');
      } else if (error.status === 400) {
        throw new Error(error.message || 'Invalid request');
      } else {
        throw new Error('Failed to add item to cart. Please try again.');
      }
    }
  };

  const updateCartItem = async (itemId: number, quantity: number) => {
    try {
      await cartApi.updateCartItem(itemId, quantity);
      await refreshCart();
    } catch (error) {
      console.error('Failed to update cart item:', error);
      throw error;
    }
  };

  const removeCartItem = async (itemId: number) => {
    try {
      await cartApi.removeCartItem(itemId);
      await refreshCart();
    } catch (error) {
      console.error('Failed to remove cart item:', error);
      throw error;
    }
  };

  const clearCart = async () => {
    try {
      await cartApi.clearCart();
      await refreshCart();
    } catch (error) {
      console.error('Failed to clear cart:', error);
      throw error;
    }
  };

  const applyCoupon = async (couponCode: string) => {
    try {
      await cartApi.applyCoupon(couponCode);
      await refreshCart();
    } catch (error) {
      console.error('Failed to apply coupon:', error);
      throw error;
    }
  };

  const removeCoupon = async () => {
    try {
      await cartApi.removeCoupon();
      await refreshCart();
    } catch (error) {
      console.error('Failed to remove coupon:', error);
      throw error;
    }
  };

  const getTotalItems = (): number => {
    return cartSummary?.items_count || 0;
  };

  const value: CartContextType = {
    cart,
    cartSummary,
    isLoading,
    addToCart,
    updateCartItem,
    removeCartItem,
    clearCart,
    applyCoupon,
    removeCoupon,
    refreshCart,
    getTotalItems,
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
};
