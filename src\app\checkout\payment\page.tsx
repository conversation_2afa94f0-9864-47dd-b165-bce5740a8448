'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Script from 'next/script';
import { ArrowLeft, CreditCard, Banknote, Shield, Calendar, MapPin } from 'lucide-react';
import { MainLayout } from '@/components/layout/MainLayout';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { LoadingButton } from '@/components/ui/LoadingSpinner';
import { PaymentMethodSelector } from '@/components/payment/PaymentMethodSelector';
import { useCart } from '@/contexts/CartContext';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/components/ui/Toaster';
import { usePaymentConfig } from '@/hooks/usePaymentConfig';
import { usePartialPayment } from '@/hooks/usePartialPayment';
import { orderApi, paymentApi } from '@/lib/api';
import { Address, Order, PaymentInitiateResponse, PartialPaymentItem } from '@/types/api';

declare global {
  interface Window {
    Razorpay: any;
  }
}

export default function CheckoutPaymentPage() {
  const [paymentMethod, setPaymentMethod] = useState<'razorpay' | 'cod'>('razorpay');
  const [paymentAmount, setPaymentAmount] = useState<'full' | 'partial'>('full');
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedAddress, setSelectedAddress] = useState<Address | null>(null);
  const [selectedSchedule, setSelectedSchedule] = useState<{ date: string; time: string } | null>(null);
  const [razorpayLoaded, setRazorpayLoaded] = useState(false);

  const { cart, cartSummary, clearCart } = useCart();
  const { user } = useAuth();
  const { showToast } = useToast();
  const { config: paymentConfig, isLoading: configLoading } = usePaymentConfig();
  const { calculation: partialPayment, calculatePartialPayment, isLoading: partialLoading } = usePartialPayment();
  const router = useRouter();

  useEffect(() => {
    // Load saved data from session storage
    const addressData = sessionStorage.getItem('selectedAddress');
    const scheduleData = sessionStorage.getItem('selectedSchedule');

    if (addressData) {
      setSelectedAddress(JSON.parse(addressData));
    }
    if (scheduleData) {
      setSelectedSchedule(JSON.parse(scheduleData));
    }

    // Redirect if no cart items
    if (!cart || cart.items.length === 0) {
      router.push('/cart');
    }
  }, [cart, router]);

  // Calculate partial payment when cart changes
  useEffect(() => {
    if (cart && cart.items.length > 0) {
      const items: PartialPaymentItem[] = cart.items.map(item => ({
        service_id: item.service,
        quantity: item.quantity,
      }));

      calculatePartialPayment(items);
    }
  }, [cart, calculatePartialPayment]);

  // Set default payment method based on config
  useEffect(() => {
    if (paymentConfig) {
      if (paymentConfig.enable_razorpay) {
        setPaymentMethod('razorpay');
      } else if (paymentConfig.enable_cod) {
        setPaymentMethod('cod');
      }
    }
  }, [paymentConfig]);

  const getPaymentAmount = () => {
    if (!cartSummary) return '0';

    if (partialPayment?.requires_partial_payment && paymentAmount === 'partial') {
      return partialPayment.partial_payment_amount;
    }

    if (paymentMethod === 'cod' && paymentConfig) {
      const codCharge = parseFloat(cartSummary.total_amount) * (parseFloat(paymentConfig.cod_charge_percentage) / 100);
      return (parseFloat(cartSummary.total_amount) + codCharge).toFixed(2);
    }

    return cartSummary.total_amount;
  };

  const handleRazorpayPayment = async () => {
    if (!selectedAddress || !selectedSchedule || !cartSummary || !paymentConfig) return;

    setIsProcessing(true);
    try {
      // Create order first
      const orderData = {
        delivery_address: selectedAddress,
        scheduled_date: selectedSchedule.date,
        scheduled_time: selectedSchedule.time + ':00',
        payment_method: 'razorpay',
        special_instructions: '',
      };

      const order = await orderApi.createOrder(orderData) as Order;

      // Initiate payment with calculated amount
      const paymentData = await paymentApi.initiatePayment({
        order_id: order.order_number,
        payment_method: 'razorpay',
        amount: getPaymentAmount(),
        currency: 'INR',
      }) as PaymentInitiateResponse;

      // Open Razorpay checkout
      const options = {
        key: paymentData.payment_gateway_data.key,
        amount: paymentData.payment_gateway_data.amount,
        currency: paymentData.payment_gateway_data.currency,
        name: 'Home Services',
        description: `Order #${order.order_number}`,
        order_id: paymentData.payment_gateway_data.razorpay_order_id,
        handler: async (response: any) => {
          try {
            // Handle successful payment
            await paymentApi.handleRazorpayCallback({
              transaction_id: paymentData.transaction_id,
              razorpay_payment_id: response.razorpay_payment_id,
              razorpay_order_id: response.razorpay_order_id,
              razorpay_signature: response.razorpay_signature,
            });

            // Clear cart and redirect
            await clearCart();
            sessionStorage.removeItem('selectedAddress');
            sessionStorage.removeItem('selectedSchedule');

            const successMessage = partialPayment?.requires_partial_payment && paymentAmount === 'partial'
              ? `Advance payment successful! Remaining ₹${partialPayment.remaining_amount} to be paid on service completion.`
              : 'Payment successful! Your order has been placed.';

            showToast({ type: 'success', title: 'Payment successful!', message: successMessage });
            router.push(`/orders/${order.order_number}`);
          } catch (error: any) {
            showToast({ type: 'error', title: 'Payment verification failed', message: error.message });
          }
        },
        prefill: {
          name: user?.name,
          email: user?.email,
          contact: user?.mobile_number,
        },
        theme: {
          color: '#3b82f6',
        },
        modal: {
          ondismiss: () => {
            setIsProcessing(false);
          },
        },
      };

      const razorpay = new window.Razorpay(options);
      razorpay.open();
    } catch (error: any) {
      showToast({ type: 'error', title: 'Payment initiation failed', message: error.message });
      setIsProcessing(false);
    }
  };

  const handleCODPayment = async () => {
    if (!selectedAddress || !selectedSchedule || !paymentConfig) return;

    setIsProcessing(true);
    try {
      const orderData = {
        delivery_address: selectedAddress,
        scheduled_date: selectedSchedule.date,
        scheduled_time: selectedSchedule.time + ':00',
        payment_method: 'cash_on_delivery',
        special_instructions: '',
      };

      const order = await orderApi.createOrder(orderData) as Order;

      // Confirm COD payment with calculated amount
      await paymentApi.confirmCODPayment({
        order_id: order.order_number,
        amount: getPaymentAmount(),
      });

      // Clear cart and redirect
      await clearCart();
      sessionStorage.removeItem('selectedAddress');
      sessionStorage.removeItem('selectedSchedule');

      const codMessage = partialPayment?.requires_partial_payment && paymentAmount === 'partial'
        ? `Order placed! Pay ₹${partialPayment.partial_payment_amount} on delivery. Remaining ₹${partialPayment.remaining_amount} on service completion.`
        : 'Order placed! Pay on delivery when service is completed.';

      showToast({ type: 'success', title: 'Order placed successfully!', message: codMessage });
      router.push(`/orders/${order.order_number}`);
    } catch (error: any) {
      showToast({ type: 'error', title: 'Order placement failed', message: error.message });
    } finally {
      setIsProcessing(false);
    }
  };

  const handlePayment = () => {
    if (paymentMethod === 'razorpay') {
      handleRazorpayPayment();
    } else if (paymentMethod === 'cod') {
      handleCODPayment();
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (timeString: string) => {
    const [hours, minutes] = timeString.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  if (!cart || !cartSummary || !selectedAddress || !selectedSchedule) {
    return (
      <MainLayout>
        <ProtectedRoute>
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16 text-center">
            <p className="text-gray-600">Loading checkout information...</p>
          </div>
        </ProtectedRoute>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <ProtectedRoute>
        <Script
          src="https://checkout.razorpay.com/v1/checkout.js"
          onLoad={() => setRazorpayLoaded(true)}
        />
        
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Progress Indicator */}
          <div className="mb-8">
            <div className="flex items-center justify-center space-x-4">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                  ✓
                </div>
                <span className="ml-2 text-sm font-medium text-green-600">Address</span>
              </div>
              <div className="w-16 h-0.5 bg-green-600"></div>
              <div className="flex items-center">
                <div className="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                  ✓
                </div>
                <span className="ml-2 text-sm font-medium text-green-600">Schedule</span>
              </div>
              <div className="w-16 h-0.5 bg-green-600"></div>
              <div className="flex items-center">
                <div className="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                  3
                </div>
                <span className="ml-2 text-sm font-medium text-primary-600">Payment</span>
              </div>
            </div>
          </div>

          {/* Back Button */}
          <button
            onClick={() => router.back()}
            className="flex items-center text-gray-600 hover:text-gray-800 mb-6"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Schedule
          </button>

          <h1 className="text-3xl font-bold text-gray-900 mb-8">Complete Your Order</h1>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Order Summary */}
            <div className="lg:col-span-2 space-y-6">
              {/* Delivery Details */}
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Delivery Details</h3>
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <MapPin className="h-5 w-5 text-gray-400 mt-0.5" />
                    <div>
                      <p className="font-medium text-gray-900">{selectedAddress.address_type}</p>
                      <p className="text-gray-600">{selectedAddress.street}</p>
                      <p className="text-gray-600">
                        {selectedAddress.city}, {selectedAddress.state} {selectedAddress.zip_code}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Calendar className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="font-medium text-gray-900">
                        {formatDate(selectedSchedule.date)} at {formatTime(selectedSchedule.time)}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Payment Method */}
              <div className="card p-6">
                {configLoading || partialLoading ? (
                  <div className="text-center py-8">
                    <p className="text-gray-600">Loading payment options...</p>
                  </div>
                ) : paymentConfig ? (
                  <PaymentMethodSelector
                    config={paymentConfig}
                    partialPayment={partialPayment}
                    selectedMethod={paymentMethod}
                    selectedAmount={paymentAmount}
                    onMethodChange={setPaymentMethod}
                    onAmountChange={setPaymentAmount}
                    totalAmount={cartSummary.total_amount}
                  />
                ) : (
                  <div className="text-center py-8">
                    <p className="text-red-600">Failed to load payment configuration</p>
                  </div>
                )}
              </div>
            </div>

            {/* Order Summary Sidebar */}
            <div className="card p-6 h-fit">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>
              
              {/* Cart Items */}
              <div className="space-y-3 mb-4">
                {cart.items.map((item) => (
                  <div key={item.id} className="flex justify-between text-sm">
                    <div>
                      <p className="font-medium text-gray-900">{item.service_title}</p>
                      <p className="text-gray-600">Qty: {item.quantity}</p>
                    </div>
                    <p className="font-medium">₹{item.total_price}</p>
                  </div>
                ))}
              </div>

              <div className="border-t pt-4 space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Subtotal</span>
                  <span>₹{cartSummary.sub_total}</span>
                </div>
                {cartSummary.discount_amount !== '0.00' && (
                  <div className="flex justify-between text-sm text-green-600">
                    <span>Discount</span>
                    <span>-₹{cartSummary.discount_amount}</span>
                  </div>
                )}

                {/* GST Breakdown */}
                {cart.tax_breakdown && cart.tax_breakdown.length > 0 ? (
                  <div className="space-y-1">
                    <div className="flex justify-between text-sm font-medium text-gray-700">
                      <span>Tax Breakdown</span>
                      <span>₹{cart.tax_amount}</span>
                    </div>
                    <div className="space-y-1 ml-4">
                      {cart.tax_breakdown.map((tax, index) => (
                        <div key={index} className="flex justify-between text-xs text-gray-600">
                          <span>{tax.type} ({tax.rate})</span>
                          <span>₹{tax.amount}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  cartSummary.tax_amount !== '0.00' && (
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Tax (GST)</span>
                      <span>₹{cartSummary.tax_amount}</span>
                    </div>
                  )
                )}

                {/* Payment Amount Display */}
                {partialPayment?.requires_partial_payment && (
                  <div className="border-t pt-2 space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">
                        {paymentAmount === 'partial' ? 'Paying Now (Advance)' : 'Total Amount'}
                      </span>
                      <span className="font-medium">
                        ₹{paymentAmount === 'partial' ? partialPayment.partial_payment_amount : getPaymentAmount()}
                      </span>
                    </div>
                    {paymentAmount === 'partial' && (
                      <div className="flex justify-between text-sm text-orange-600">
                        <span>Remaining (On Service)</span>
                        <span>₹{partialPayment.remaining_amount}</span>
                      </div>
                    )}
                  </div>
                )}

                <div className="border-t pt-2">
                  <div className="flex justify-between font-semibold text-lg">
                    <span>
                      {partialPayment?.requires_partial_payment && paymentAmount === 'partial'
                        ? 'Paying Now'
                        : 'Total'}
                    </span>
                    <span>₹{getPaymentAmount()}</span>
                  </div>
                </div>
              </div>

              <LoadingButton
                onClick={handlePayment}
                isLoading={isProcessing}
                disabled={(paymentMethod === 'razorpay' && !razorpayLoaded) || configLoading || partialLoading}
                className="w-full btn-primary mt-6"
              >
                {paymentMethod === 'razorpay'
                  ? `Pay ₹${getPaymentAmount()}`
                  : `Place Order - ₹${getPaymentAmount()}`}
              </LoadingButton>

              <p className="text-xs text-gray-500 text-center mt-4">
                By placing this order, you agree to our Terms of Service and Privacy Policy.
              </p>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    </MainLayout>
  );
}
