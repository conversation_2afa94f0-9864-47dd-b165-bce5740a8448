'use client';

import React, { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft, Calendar, MapPin, CreditCard, Package, Download, X } from 'lucide-react';
import { MainLayout } from '@/components/layout/MainLayout';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { LoadingPage, LoadingButton } from '@/components/ui/LoadingSpinner';
import { useToast } from '@/components/ui/Toaster';
import { orderApi } from '@/lib/api';
import { Order } from '@/types/api';

export default function OrderDetailPage() {
  const [order, setOrder] = useState<Order | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [cancellingOrder, setCancellingOrder] = useState(false);
  const [cancelReason, setCancelReason] = useState('');
  const [showCancelModal, setShowCancelModal] = useState(false);

  const params = useParams();
  const router = useRouter();
  const { showToast } = useToast();

  const orderNumber = params.orderNumber as string;

  useEffect(() => {
    fetchOrderDetail();
  }, [orderNumber]);

  const fetchOrderDetail = async () => {
    try {
      const data = await orderApi.getOrderDetail(orderNumber);
      setOrder(data as Order);
    } catch (error: any) {
      showToast({ type: 'error', title: 'Failed to load order details', message: error.message });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelOrder = async () => {
    if (!cancelReason.trim()) {
      showToast({ type: 'error', title: 'Please provide a cancellation reason' });
      return;
    }

    setCancellingOrder(true);
    try {
      await orderApi.cancelOrder(orderNumber, cancelReason);
      await fetchOrderDetail(); // Refresh order details
      setShowCancelModal(false);
      setCancelReason('');
      showToast({ type: 'success', title: 'Order cancelled successfully' });
    } catch (error: any) {
      showToast({ type: 'error', title: 'Failed to cancel order', message: error.message });
    } finally {
      setCancellingOrder(false);
    }
  };

  const handlePrintInvoice = () => {
    // Simple print functionality
    window.print();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'confirmed':
        return 'bg-blue-100 text-blue-800';
      case 'in_progress':
        return 'bg-purple-100 text-purple-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const canCancelOrder = (order: Order) => {
    return ['pending', 'confirmed'].includes(order.status);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (timeString: string) => {
    const [hours, minutes] = timeString.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  if (isLoading) {
    return (
      <MainLayout>
        <ProtectedRoute>
          <LoadingPage message="Loading order details..." />
        </ProtectedRoute>
      </MainLayout>
    );
  }

  if (!order) {
    return (
      <MainLayout>
        <ProtectedRoute>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Order not found</h1>
            <Link href="/orders" className="btn-primary">
              Back to Orders
            </Link>
          </div>
        </ProtectedRoute>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <ProtectedRoute>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="mb-8">
            <Link
              href="/orders"
              className="flex items-center text-gray-600 hover:text-gray-800 mb-4"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Orders
            </Link>
            
            <div className="flex items-start justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 mb-2">
                  Order #{order.order_number}
                </h1>
                <p className="text-gray-600">
                  Placed on {formatDate(order.created_at)}
                </p>
              </div>
              <div className="flex space-x-2">
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(order.status)}`}>
                  {order.status.replace('_', ' ').toUpperCase()}
                </span>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${getPaymentStatusColor(order.payment_status)}`}>
                  {order.payment_status.toUpperCase()}
                </span>
              </div>
            </div>
          </div>

          {/* Order Details */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2 space-y-6">
              {/* Service Details */}
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Service Details</h3>
                <div className="space-y-4">
                  {/* This would typically come from order items, but since we don't have that in the API response, we'll show a placeholder */}
                  <div className="flex items-center space-x-4">
                    <Package className="h-8 w-8 text-primary-600" />
                    <div>
                      <p className="font-medium text-gray-900">Professional Home Service</p>
                      <p className="text-sm text-gray-600">Scheduled service booking</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Schedule & Address */}
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Service Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <div className="flex items-center space-x-2 mb-2">
                      <Calendar className="h-5 w-5 text-primary-600" />
                      <span className="font-medium text-gray-900">Scheduled Date & Time</span>
                    </div>
                    <p className="text-gray-700">
                      {formatDate(order.scheduled_date)} at {formatTime(order.scheduled_time)}
                    </p>
                  </div>

                  <div>
                    <div className="flex items-center space-x-2 mb-2">
                      <MapPin className="h-5 w-5 text-primary-600" />
                      <span className="font-medium text-gray-900">Service Address</span>
                    </div>
                    <div className="text-gray-700">
                      <p>{order.delivery_address.street}</p>
                      <p>{order.delivery_address.city}, {order.delivery_address.state} {order.delivery_address.zip_code}</p>
                      {order.delivery_address.landmark && (
                        <p className="text-sm text-gray-600">Near {order.delivery_address.landmark}</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Payment Information */}
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Payment Information</h3>
                <div className="flex items-center space-x-2 mb-4">
                  <CreditCard className="h-5 w-5 text-primary-600" />
                  <span className="font-medium text-gray-900">Payment Method</span>
                </div>
                <p className="text-gray-700 mb-4">
                  {order.payment_method === 'razorpay' ? 'Online Payment' : 'Cash on Delivery'}
                </p>
                
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Subtotal</span>
                    <span>₹{order.sub_total}</span>
                  </div>
                  {order.discount_amount !== '0.00' && (
                    <div className="flex justify-between text-green-600">
                      <span>Discount</span>
                      <span>-₹{order.discount_amount}</span>
                    </div>
                  )}

                  {/* GST Breakdown */}
                  {order.tax_breakdown && order.tax_breakdown.length > 0 ? (
                    <div className="space-y-1">
                      <div className="flex justify-between font-medium text-gray-700">
                        <span>Tax Breakdown</span>
                        <span>₹{order.tax_amount}</span>
                      </div>
                      <div className="space-y-1 ml-4">
                        {order.tax_breakdown.map((tax, index) => (
                          <div key={index} className="flex justify-between text-sm text-gray-600">
                            <span>{tax.type} ({tax.rate})</span>
                            <span>₹{tax.amount}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : (
                    order.tax_amount !== '0.00' && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">Tax (GST)</span>
                        <span>₹{order.tax_amount}</span>
                      </div>
                    )
                  )}

                  <div className="border-t pt-2">
                    <div className="flex justify-between font-semibold text-lg">
                      <span>Total Amount</span>
                      <span>₹{order.total_amount}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Special Instructions */}
              {order.special_instructions && (
                <div className="card p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Special Instructions</h3>
                  <p className="text-gray-700">{order.special_instructions}</p>
                </div>
              )}

              {/* Cancellation Reason */}
              {order.status === 'cancelled' && order.cancellation_reason && (
                <div className="card p-6 bg-red-50 border-red-200">
                  <h3 className="text-lg font-semibold text-red-900 mb-4">Cancellation Reason</h3>
                  <p className="text-red-700">{order.cancellation_reason}</p>
                </div>
              )}
            </div>

            {/* Actions Sidebar */}
            <div className="space-y-4">
              {order.payment_status === 'paid' && (
                <button
                  onClick={handlePrintInvoice}
                  className="w-full btn-outline flex items-center justify-center"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Print Invoice
                </button>
              )}

              {canCancelOrder(order) && (
                <button
                  onClick={() => setShowCancelModal(true)}
                  className="w-full btn-danger flex items-center justify-center"
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancel Order
                </button>
              )}

              <div className="card p-4">
                <h4 className="font-medium text-gray-900 mb-2">Need Help?</h4>
                <p className="text-sm text-gray-600 mb-3">
                  Contact our support team for any assistance with your order.
                </p>
                <Link href="/contact" className="text-sm text-primary-600 hover:text-primary-700">
                  Contact Support →
                </Link>
              </div>
            </div>
          </div>

          {/* Cancel Order Modal */}
          {showCancelModal && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Cancel Order</h3>
                <p className="text-gray-600 mb-4">
                  Please provide a reason for cancelling this order:
                </p>
                <textarea
                  value={cancelReason}
                  onChange={(e) => setCancelReason(e.target.value)}
                  placeholder="Enter cancellation reason..."
                  className="w-full input mb-4"
                  rows={3}
                />
                <div className="flex space-x-3">
                  <LoadingButton
                    onClick={handleCancelOrder}
                    isLoading={cancellingOrder}
                    className="flex-1 btn-danger"
                  >
                    Cancel Order
                  </LoadingButton>
                  <button
                    onClick={() => {
                      setShowCancelModal(false);
                      setCancelReason('');
                    }}
                    className="flex-1 btn-secondary"
                  >
                    Keep Order
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </ProtectedRoute>
    </MainLayout>
  );
}
