'use client';

import React, { useState, useEffect } from 'react';
import { MainLayout } from '@/components/layout/MainLayout';
import { useCart } from '@/contexts/CartContext';
import { useAuth } from '@/contexts/AuthContext';
import { cartApi } from '@/lib/api';

export default function TestGSTPage() {
  const { cart, cartSummary, refreshCart } = useCart();
  const { isAuthenticated, user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<any>(null);

  const testGSTAPI = async () => {
    if (!isAuthenticated) {
      setTestResults({ error: 'User must be authenticated to test cart API' });
      return;
    }

    setIsLoading(true);
    try {
      // Test cart API
      const cartData = await cartApi.getCart();
      const summaryData = await cartApi.getCartSummary();

      setTestResults({
        cart: cartData,
        summary: summaryData,
        timestamp: new Date().toISOString(),
        hasGSTBreakdown: !!(cartData?.tax_breakdown && cartData.tax_breakdown.length > 0),
        gstFields: {
          cgst_amount: cartData?.cgst_amount,
          sgst_amount: cartData?.sgst_amount,
          igst_amount: cartData?.igst_amount,
          service_charge: cartData?.service_charge,
          tax_breakdown: cartData?.tax_breakdown
        }
      });

      console.log('GST API Test Results:', {
        cart: cartData,
        summary: summaryData,
        hasGSTBreakdown: !!(cartData?.tax_breakdown && cartData.tax_breakdown.length > 0)
      });
    } catch (error: any) {
      console.error('GST API Test Failed:', error);
      setTestResults({ error: error.message || 'Unknown error occurred' });
    } finally {
      setIsLoading(false);
    }
  };

  const testAddToCart = async () => {
    if (!isAuthenticated) {
      alert('Please login first to test cart functionality');
      return;
    }

    try {
      setIsLoading(true);
      await cartApi.addToCart(1, 1); // Add service ID 1 with quantity 1
      await refreshCart();
      await testGSTAPI();
    } catch (error: any) {
      console.error('Failed to add to cart:', error);
      alert(`Failed to add item: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testRemoveFromCart = async (itemId: number) => {
    if (!isAuthenticated) {
      alert('Please login first to test cart functionality');
      return;
    }

    try {
      setIsLoading(true);
      await cartApi.removeCartItem(itemId);
      await refreshCart();
      await testGSTAPI();
    } catch (error: any) {
      console.error('Failed to remove from cart:', error);
      alert(`Failed to remove item: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (isAuthenticated) {
      testGSTAPI();
    }
  }, [isAuthenticated]);

  return (
    <MainLayout>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">GST Breakdown Test</h1>
          <p className="text-gray-600">Testing the new GST breakdown API integration</p>

          {/* Authentication Status */}
          <div className="mt-4 p-4 rounded-lg border">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">
                  Authentication Status:
                  <span className={`ml-2 ${isAuthenticated ? 'text-green-600' : 'text-red-600'}`}>
                    {isAuthenticated ? '✅ Authenticated' : '❌ Not Authenticated'}
                  </span>
                </p>
                {user && (
                  <p className="text-sm text-gray-600 mt-1">
                    Logged in as: {user.name} ({user.mobile_number})
                  </p>
                )}
              </div>
              {!isAuthenticated && (
                <button
                  onClick={() => window.location.href = '/auth/login'}
                  className="btn-primary"
                >
                  Login to Test
                </button>
              )}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Current Cart Context */}
          <div className="card p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Cart Context Data</h2>
            
            {cart && (
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium text-gray-700 mb-2">Basic Info</h3>
                  <div className="text-sm space-y-1">
                    <p>Items: {cart.items_count}</p>
                    <p>Subtotal: ₹{cart.sub_total}</p>
                    <p>Total: ₹{cart.total_amount}</p>
                  </div>
                </div>

                <div>
                  <h3 className="font-medium text-gray-700 mb-2">GST Breakdown</h3>
                  <div className="text-sm space-y-1">
                    <p>CGST: ₹{cart.cgst_amount || '0.00'}</p>
                    <p>SGST: ₹{cart.sgst_amount || '0.00'}</p>
                    <p>IGST: ₹{cart.igst_amount || '0.00'}</p>
                    <p>Service Charge: ₹{cart.service_charge || '0.00'}</p>
                  </div>
                </div>

                {cart.tax_breakdown && cart.tax_breakdown.length > 0 && (
                  <div>
                    <h3 className="font-medium text-gray-700 mb-2">Tax Breakdown Array</h3>
                    <div className="space-y-1">
                      {cart.tax_breakdown.map((tax, index) => (
                        <div key={index} className="text-sm flex justify-between">
                          <span>{tax.type} ({tax.rate})</span>
                          <span>₹{tax.amount}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {!cart && (
              <p className="text-gray-500">No cart data available</p>
            )}
          </div>

          {/* API Test Results */}
          <div className="card p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-gray-900">API Test Results</h2>
              <button
                onClick={testGSTAPI}
                disabled={isLoading}
                className="btn-outline text-sm"
              >
                {isLoading ? 'Testing...' : 'Retest API'}
              </button>
            </div>

            {testResults && (
              <div className="space-y-4">
                {testResults.error ? (
                  <div className="text-red-600 text-sm">
                    <p className="font-medium">Error:</p>
                    <p>{testResults.error}</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div>
                      <h3 className="font-medium text-gray-700 mb-2">GST Fields Check</h3>
                      <div className="bg-gray-50 p-3 rounded text-sm space-y-1">
                        <p><strong>Has tax_breakdown:</strong> {testResults.cart?.tax_breakdown ? 'Yes' : 'No'}</p>
                        <p><strong>tax_breakdown length:</strong> {testResults.cart?.tax_breakdown?.length || 0}</p>
                        <p><strong>CGST:</strong> ₹{testResults.cart?.cgst_amount || '0.00'}</p>
                        <p><strong>SGST:</strong> ₹{testResults.cart?.sgst_amount || '0.00'}</p>
                        <p><strong>IGST:</strong> ₹{testResults.cart?.igst_amount || '0.00'}</p>
                        <p><strong>Service Charge:</strong> ₹{testResults.cart?.service_charge || '0.00'}</p>
                        <p><strong>Total Tax:</strong> ₹{testResults.cart?.tax_amount || '0.00'}</p>
                        {testResults.cart?.tax_breakdown && testResults.cart.tax_breakdown.length > 0 && (
                          <div className="mt-2">
                            <p><strong>Tax Breakdown Array:</strong></p>
                            <ul className="ml-4 list-disc">
                              {testResults.cart.tax_breakdown.map((tax: any, index: number) => (
                                <li key={index}>{tax.type} ({tax.rate}): ₹{tax.amount}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    </div>

                    <div>
                      <h3 className="font-medium text-gray-700 mb-2">Full Cart API Response</h3>
                      <div className="bg-gray-50 p-3 rounded text-xs max-h-60 overflow-y-auto">
                        <pre>{JSON.stringify(testResults.cart, null, 2)}</pre>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Test Cart Items */}
        {cart && cart.items && cart.items.length > 0 && (
          <div className="mt-8 card p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Cart Items (Test Deletion)</h3>
            <div className="space-y-2">
              {cart.items.map((item) => (
                <div key={item.id} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                  <div>
                    <p className="font-medium">{item.service_title}</p>
                    <p className="text-sm text-gray-600">Qty: {item.quantity} | Price: ₹{item.total_price}</p>
                  </div>
                  <button
                    onClick={() => testRemoveFromCart(item.id)}
                    disabled={isLoading}
                    className="btn-danger text-sm"
                  >
                    {isLoading ? 'Removing...' : 'Remove'}
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="mt-8 flex flex-wrap gap-4">
          <button
            onClick={refreshCart}
            disabled={isLoading}
            className="btn-primary"
          >
            Refresh Cart Context
          </button>

          <button
            onClick={testAddToCart}
            disabled={isLoading}
            className="btn-outline"
          >
            {isLoading ? 'Adding...' : 'Add Test Item'}
          </button>

          <button
            onClick={() => window.location.href = '/cart'}
            className="btn-outline"
          >
            Go to Cart Page
          </button>

          {testResults && testResults.hasGSTBreakdown && (
            <div className="text-green-600 font-medium">
              ✅ GST Breakdown Working!
            </div>
          )}

          {testResults && !testResults.hasGSTBreakdown && !testResults.error && (
            <div className="text-orange-600 font-medium">
              ⚠️ No GST Breakdown (Cart might be empty)
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
}
